# Component Library - Ocean Soul Sparkles Admin Panel

## Overview

This component library defines the building blocks for the redesigned admin panel, ensuring consistency, accessibility, and maintainability across the entire system. Each component follows the Ocean Soul Sparkles design system and includes comprehensive specifications for implementation.

## Base Components

### Button Component

#### Variants
```jsx
// Primary Button - Main actions
<Button variant="primary" size="md">
  Create Booking
</Button>

// Secondary Button - Secondary actions  
<Button variant="secondary" size="md">
  Cancel
</Button>

// Ghost Button - Subtle actions
<Button variant="ghost" size="sm">
  Edit
</Button>

// Danger Button - Destructive actions
<Button variant="danger" size="md">
  Delete Customer
</Button>
```

#### Specifications
- **Minimum Touch Target**: 44px height for accessibility
- **Border Radius**: 8px for modern appearance
- **Transition**: 200ms ease for smooth interactions
- **Focus State**: 2px outline with brand color
- **Loading State**: Spinner with disabled interaction

#### States
- **Default**: Normal appearance
- **Hover**: Slight elevation and color change
- **Active**: Pressed appearance
- **Disabled**: Reduced opacity and no interaction
- **Loading**: Spinner with disabled state

### Input Component

#### Types
```jsx
// Text Input with floating label
<Input 
  label="Customer Name" 
  type="text" 
  required 
  placeholder="Enter customer name"
/>

// Email Input with validation
<Input 
  label="Email Address" 
  type="email" 
  validation="email"
  error="Please enter a valid email"
/>

// Password Input with strength indicator
<Input 
  label="Password" 
  type="password" 
  showStrength 
  requirements={['8+ characters', 'uppercase', 'number']}
/>

// Search Input with icon
<Input 
  label="Search Customers" 
  type="search" 
  icon={<SearchIcon />}
  onSearch={handleSearch}
/>
```

#### Features
- **Floating Labels**: Labels animate to top when focused/filled
- **Validation States**: Success, error, and warning states
- **Auto-complete**: Smart suggestions and auto-fill
- **Accessibility**: Proper ARIA labels and keyboard navigation

### Card Component

#### Variants
```jsx
// Basic Card
<Card>
  <Card.Header>
    <Card.Title>Customer Details</Card.Title>
  </Card.Header>
  <Card.Content>
    Customer information content
  </Card.Content>
</Card>

// Interactive Card with hover effects
<Card interactive onClick={handleClick}>
  <Card.Content>
    Clickable card content
  </Card.Content>
</Card>

// Status Card with colored border
<Card status="success">
  <Card.Content>
    Success status content
  </Card.Content>
</Card>
```

#### Specifications
- **Shadow**: Subtle drop shadow with hover elevation
- **Border Radius**: 12px for modern appearance
- **Padding**: 24px for comfortable content spacing
- **Background**: White with subtle border

## Form Components

### Form Group Component

#### Structure
```jsx
<FormGroup>
  <FormLabel required>Service Type</FormLabel>
  <Select
    options={serviceOptions}
    placeholder="Select a service"
    error={errors.service}
  />
  <FormHelperText>
    Choose the primary service for this booking
  </FormHelperText>
  <FormErrorText>{errors.service}</FormErrorText>
</FormGroup>
```

#### Features
- **Consistent Spacing**: Standardized spacing between form elements
- **Error Handling**: Clear error states and messages
- **Helper Text**: Contextual guidance for users
- **Required Indicators**: Visual indication of required fields

### Select Component

#### Variants
```jsx
// Single Select
<Select
  options={[
    { value: 'haircut', label: 'Haircut' },
    { value: 'coloring', label: 'Hair Coloring' },
    { value: 'styling', label: 'Hair Styling' }
  ]}
  placeholder="Select service"
/>

// Multi Select
<Select
  multiple
  options={customerTags}
  placeholder="Select tags"
  maxSelected={5}
/>

// Searchable Select
<Select
  searchable
  options={customerList}
  placeholder="Search customers"
  onSearch={handleCustomerSearch}
/>
```

#### Features
- **Keyboard Navigation**: Arrow keys, Enter, Escape support
- **Search Functionality**: Filter options by typing
- **Custom Rendering**: Support for custom option rendering
- **Accessibility**: Screen reader support and ARIA attributes

## Data Display Components

### Table Component

#### Basic Structure
```jsx
<Table>
  <Table.Header>
    <Table.Row>
      <Table.HeaderCell sortable onSort={handleSort}>
        Customer Name
      </Table.HeaderCell>
      <Table.HeaderCell>Service</Table.HeaderCell>
      <Table.HeaderCell>Date</Table.HeaderCell>
      <Table.HeaderCell>Status</Table.HeaderCell>
      <Table.HeaderCell>Actions</Table.HeaderCell>
    </Table.Row>
  </Table.Header>
  <Table.Body>
    {bookings.map(booking => (
      <Table.Row key={booking.id}>
        <Table.Cell>{booking.customerName}</Table.Cell>
        <Table.Cell>{booking.service}</Table.Cell>
        <Table.Cell>{formatDate(booking.date)}</Table.Cell>
        <Table.Cell>
          <StatusBadge status={booking.status} />
        </Table.Cell>
        <Table.Cell>
          <ActionMenu booking={booking} />
        </Table.Cell>
      </Table.Row>
    ))}
  </Table.Body>
</Table>
```

#### Features
- **Sortable Columns**: Click to sort with visual indicators
- **Responsive Design**: Horizontal scroll on mobile with sticky columns
- **Row Selection**: Checkbox selection for bulk actions
- **Pagination**: Built-in pagination with customizable page sizes
- **Loading States**: Skeleton loading for better UX

### Status Badge Component

#### Variants
```jsx
// Success Status
<StatusBadge status="confirmed" variant="success">
  Confirmed
</StatusBadge>

// Warning Status
<StatusBadge status="pending" variant="warning">
  Pending
</StatusBadge>

// Error Status
<StatusBadge status="cancelled" variant="error">
  Cancelled
</StatusBadge>

// Info Status
<StatusBadge status="in-progress" variant="info">
  In Progress
</StatusBadge>
```

#### Specifications
- **Color Coding**: Semantic colors for different statuses
- **Icon Support**: Optional icons for better recognition
- **Size Variants**: Small, medium, large sizes
- **Accessibility**: Proper contrast and screen reader support

## Layout Components

### Modal Component

#### Structure
```jsx
<Modal 
  isOpen={isModalOpen} 
  onClose={handleClose}
  size="lg"
  title="Create New Booking"
>
  <Modal.Header>
    <Modal.Title>Create New Booking</Modal.Title>
    <Modal.CloseButton onClick={handleClose} />
  </Modal.Header>
  
  <Modal.Body>
    <BookingForm onSubmit={handleSubmit} />
  </Modal.Body>
  
  <Modal.Footer>
    <Button variant="secondary" onClick={handleClose}>
      Cancel
    </Button>
    <Button variant="primary" onClick={handleSave}>
      Save Booking
    </Button>
  </Modal.Footer>
</Modal>
```

#### Features
- **Focus Management**: Traps focus within modal
- **Backdrop Click**: Closes modal when clicking outside
- **Escape Key**: Closes modal with Escape key
- **Scroll Management**: Prevents body scroll when open
- **Animation**: Smooth open/close animations

### Sidebar Component

#### Navigation Structure
```jsx
<Sidebar isOpen={sidebarOpen} onClose={closeSidebar}>
  <Sidebar.Header>
    <Logo />
    <Sidebar.CloseButton />
  </Sidebar.Header>
  
  <Sidebar.Navigation>
    <NavigationGroup title="Overview">
      <NavigationItem 
        href="/admin" 
        icon={<DashboardIcon />}
        active={pathname === '/admin'}
      >
        Dashboard
      </NavigationItem>
    </NavigationGroup>
    
    <NavigationGroup title="Operations" collapsible>
      <NavigationItem href="/admin/bookings" icon={<CalendarIcon />}>
        Bookings
      </NavigationItem>
      <NavigationItem href="/admin/pos" icon={<CashIcon />}>
        POS Terminal
      </NavigationItem>
    </NavigationGroup>
  </Sidebar.Navigation>
  
  <Sidebar.Footer>
    <UserProfile />
    <SignOutButton />
  </Sidebar.Footer>
</Sidebar>
```

#### Features
- **Responsive Behavior**: Overlay on mobile, fixed on desktop
- **Collapsible Groups**: Expandable navigation sections
- **Active States**: Clear indication of current page
- **Search**: Quick navigation search functionality

## Feedback Components

### Toast Notification Component

#### Variants
```jsx
// Success Toast
<Toast variant="success" duration={5000}>
  Booking created successfully!
</Toast>

// Error Toast
<Toast variant="error" duration={0} dismissible>
  Failed to save customer. Please try again.
</Toast>

// Info Toast with action
<Toast variant="info" action={<Button size="sm">Undo</Button>}>
  Customer deleted
</Toast>
```

#### Features
- **Auto Dismiss**: Configurable auto-dismiss timing
- **Manual Dismiss**: Close button for user control
- **Action Support**: Optional action buttons
- **Stacking**: Multiple toasts stack vertically
- **Accessibility**: Screen reader announcements

### Loading Component

#### Variants
```jsx
// Spinner Loading
<Loading variant="spinner" size="md" />

// Skeleton Loading for content
<Loading variant="skeleton">
  <Loading.Text lines={3} />
  <Loading.Avatar />
  <Loading.Button />
</Loading>

// Progress Bar Loading
<Loading variant="progress" value={75} max={100} />
```

#### Features
- **Multiple Variants**: Spinner, skeleton, progress bar
- **Size Options**: Small, medium, large sizes
- **Accessibility**: Proper ARIA labels and live regions
- **Customization**: Configurable colors and animations

## Data Visualization Components

### Chart Component

#### Chart Types
```jsx
// Line Chart for trends
<Chart type="line" data={revenueData} options={chartOptions} />

// Bar Chart for comparisons
<Chart type="bar" data={serviceData} options={chartOptions} />

// Pie Chart for distributions
<Chart type="pie" data={customerSegments} options={chartOptions} />

// Donut Chart for metrics
<Chart type="donut" data={statusDistribution} options={chartOptions} />
```

#### Features
- **Interactive**: Hover effects and click events
- **Responsive**: Adapts to container size
- **Accessible**: Keyboard navigation and screen reader support
- **Customizable**: Extensive styling and configuration options

### Metric Card Component

#### Structure
```jsx
<MetricCard>
  <MetricCard.Icon>
    <RevenueIcon />
  </MetricCard.Icon>
  <MetricCard.Content>
    <MetricCard.Label>Total Revenue</MetricCard.Label>
    <MetricCard.Value>$12,450</MetricCard.Value>
    <MetricCard.Change trend="up" percentage={12.5}>
      +12.5% from last month
    </MetricCard.Change>
  </MetricCard.Content>
</MetricCard>
```

#### Features
- **Trend Indicators**: Visual indicators for positive/negative trends
- **Icon Support**: Customizable icons for different metrics
- **Comparison Data**: Show changes from previous periods
- **Loading States**: Skeleton loading for data fetching

## Specialized Components

### Calendar Component

#### Features
```jsx
<Calendar
  view="month" // day, week, month
  events={bookings}
  onEventClick={handleEventClick}
  onDateClick={handleDateClick}
  onEventDrop={handleEventDrop}
  timeSlots={timeSlots}
  workingHours={{ start: '09:00', end: '18:00' }}
/>
```

#### Capabilities
- **Multiple Views**: Day, week, month views
- **Drag & Drop**: Move events between time slots
- **Event Creation**: Click to create new events
- **Responsive**: Adapts to screen size
- **Accessibility**: Keyboard navigation support

### Search Component

#### Advanced Search
```jsx
<Search
  placeholder="Search customers, bookings, services..."
  filters={[
    { key: 'type', label: 'Type', options: ['customer', 'booking', 'service'] },
    { key: 'status', label: 'Status', options: ['active', 'inactive'] },
    { key: 'date', label: 'Date Range', type: 'daterange' }
  ]}
  onSearch={handleSearch}
  onFilterChange={handleFilterChange}
  suggestions={searchSuggestions}
/>
```

#### Features
- **Global Search**: Search across multiple data types
- **Advanced Filters**: Multiple filter criteria
- **Auto-complete**: Smart suggestions as user types
- **Recent Searches**: Show recent search history
- **Keyboard Shortcuts**: Quick access with keyboard

## Implementation Guidelines

### Component Development Standards

#### Code Structure
- Use TypeScript for type safety
- Implement proper prop validation
- Include comprehensive JSDoc comments
- Follow React best practices and hooks

#### Accessibility Requirements
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Proper ARIA attributes

#### Testing Requirements
- Unit tests for all components
- Integration tests for complex interactions
- Visual regression tests
- Accessibility testing

#### Documentation Standards
- Storybook stories for all components
- Usage examples and best practices
- API documentation
- Design specifications

This component library provides a comprehensive foundation for building a consistent, accessible, and maintainable admin panel that reflects the premium quality of the Ocean Soul Sparkles brand.
