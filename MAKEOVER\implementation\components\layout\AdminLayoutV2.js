import { useState, useEffect } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '@/contexts/AuthContext'
import SidebarV2 from './SidebarV2'
import HeaderV2 from './HeaderV2'
import NotificationCenter from '../ui/NotificationCenter'
import QuickActions from '../ui/QuickActions'
import { useFeatureFlag, FEATURE_FLAGS } from '../../utils/feature-flags'

export default function AdminLayoutV2({ children, title, showQuickActions = true }) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [notificationsOpen, setNotificationsOpen] = useState(false)
  const [quickActionsOpen, setQuickActionsOpen] = useState(false)
  const { user, role, hasAdminAccess } = useAuth()
  const router = useRouter()
  const isNewUIEnabled = useFeatureFlag(FEATURE_FLAGS.NAVIGATION_V2)

  // Close sidebar on route change (mobile)
  useEffect(() => {
    const handleRouteChange = () => setSidebarOpen(false)
    router.events.on('routeChangeStart', handleRouteChange)
    return () => router.events.off('routeChangeStart', handleRouteChange)
  }, [router])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Cmd/Ctrl + K for quick actions
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setQuickActionsOpen(true)
      }
      // Escape to close modals
      if (e.key === 'Escape') {
        setSidebarOpen(false)
        setNotificationsOpen(false)
        setQuickActionsOpen(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  if (!isNewUIEnabled) {
    // Fallback to original layout if feature flag is disabled
    const OriginalAdminLayout = require('@/components/admin/AdminLayout').default
    return <OriginalAdminLayout title={title}>{children}</OriginalAdminLayout>
  }

  return (
    <div className="admin-layout-v2">
      <Head>
        <title>{title ? `${title} | ` : ''}Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Ocean Soul Sparkles Admin Panel - Premium Business Management" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="true" />
        <link 
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;600;700&display=swap" 
          rel="stylesheet" 
        />
      </Head>

      {/* Sidebar */}
      <SidebarV2 
        isOpen={sidebarOpen} 
        onClose={() => setSidebarOpen(false)}
        user={user}
        role={role}
        hasAdminAccess={hasAdminAccess}
      />

      {/* Mobile Sidebar Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Main Content Area */}
      <div className="main-content-area">
        {/* Header */}
        <HeaderV2
          title={title}
          onMenuClick={() => setSidebarOpen(true)}
          onNotificationsClick={() => setNotificationsOpen(true)}
          onQuickActionsClick={() => setQuickActionsOpen(true)}
          user={user}
          role={role}
        />

        {/* Page Content */}
        <main className="page-content">
          <div className="content-wrapper">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="content-container"
            >
              {children}
            </motion.div>
          </div>
        </main>

        {/* Floating Action Button for Quick Actions */}
        {showQuickActions && (
          <motion.button
            className="quick-actions-fab"
            onClick={() => setQuickActionsOpen(true)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5 }}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </motion.button>
        )}
      </div>

      {/* Notification Center */}
      <NotificationCenter 
        isOpen={notificationsOpen}
        onClose={() => setNotificationsOpen(false)}
      />

      {/* Quick Actions Modal */}
      <QuickActions
        isOpen={quickActionsOpen}
        onClose={() => setQuickActionsOpen(false)}
      />

      <style jsx>{`
        .admin-layout-v2 {
          display: flex;
          min-height: 100vh;
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          font-family: var(--font-primary);
        }

        .main-content-area {
          flex: 1;
          display: flex;
          flex-direction: column;
          margin-left: 0;
          transition: margin-left 0.3s ease;
        }

        @media (min-width: 1024px) {
          .main-content-area {
            margin-left: 320px;
          }
        }

        .page-content {
          flex: 1;
          overflow-y: auto;
          background: transparent;
        }

        .content-wrapper {
          min-height: calc(100vh - 80px);
          padding: var(--space-6);
        }

        .content-container {
          max-width: 1400px;
          margin: 0 auto;
        }

        .quick-actions-fab {
          position: fixed;
          bottom: var(--space-6);
          right: var(--space-6);
          width: 56px;
          height: 56px;
          background: var(--gradient-ocean);
          color: white;
          border: none;
          border-radius: 50%;
          box-shadow: 0 8px 32px rgba(11, 36, 71, 0.3);
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 30;
          transition: all 0.2s ease;
        }

        .quick-actions-fab:hover {
          box-shadow: 0 12px 40px rgba(11, 36, 71, 0.4);
        }

        @media (max-width: 768px) {
          .content-wrapper {
            padding: var(--space-4);
          }
          
          .quick-actions-fab {
            bottom: var(--space-4);
            right: var(--space-4);
            width: 48px;
            height: 48px;
          }
        }

        /* Smooth transitions for all interactive elements */
        * {
          transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, 
                      transform 0.2s ease, box-shadow 0.2s ease;
        }

        /* Focus styles for accessibility */
        *:focus {
          outline: 2px solid var(--ocean-light);
          outline-offset: 2px;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
          width: 8px;
        }

        ::-webkit-scrollbar-track {
          background: var(--neutral-100);
        }

        ::-webkit-scrollbar-thumb {
          background: var(--neutral-300);
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: var(--neutral-400);
        }

        /* Loading animation for content */
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .content-container > * {
          animation: fadeInUp 0.3s ease forwards;
        }

        /* Responsive typography */
        @media (max-width: 640px) {
          .content-container {
            font-size: 14px;
          }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
          .admin-layout-v2 {
            background: white;
          }
          
          .quick-actions-fab {
            background: var(--ocean-deep);
            border: 2px solid var(--neutral-900);
          }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          * {
            transition: none !important;
            animation: none !important;
          }
        }

        /* Print styles */
        @media print {
          .quick-actions-fab {
            display: none;
          }
          
          .main-content-area {
            margin-left: 0;
          }
        }
      `}</style>
    </div>
  )
}
