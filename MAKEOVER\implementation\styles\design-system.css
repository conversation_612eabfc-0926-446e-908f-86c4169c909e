/**
 * Ocean Soul Sparkles Admin Panel Design System
 * Version 2.0 - Premium UI/UX Enhancement
 * 
 * This CSS file contains the complete design system tokens and utilities
 * for the redesigned admin panel, ensuring consistency and maintainability.
 */

/* ===== CSS CUSTOM PROPERTIES (DESIGN TOKENS) ===== */

:root {
  /* === OCEAN SOUL SPARKLES BRAND COLORS === */
  
  /* Primary Ocean Blues */
  --ocean-deep: #0B2447;          /* Deep ocean blue - primary dark */
  --ocean-medium: #19376D;        /* Medium ocean blue - primary */
  --ocean-light: #576CBC;         /* Light ocean blue - primary light */
  --ocean-surface: #A5D7E8;       /* Surface blue - accent */
  
  /* Sparkle Accents */
  --sparkle-gold: #FFD700;        /* Gold sparkle - premium accent */
  --sparkle-silver: #C0C0C0;      /* Silver sparkle - secondary accent */
  --sparkle-pearl: #F8F8FF;       /* Pearl white - background accent */
  
  /* Ocean Gradients */
  --gradient-ocean: linear-gradient(135deg, #0B2447 0%, #19376D 50%, #576CBC 100%);
  --gradient-sparkle: linear-gradient(45deg, #FFD700 0%, #C0C0C0 50%, #FFD700 100%);
  --gradient-surface: linear-gradient(180deg, #A5D7E8 0%, #F8F8FF 100%);
  --gradient-background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  
  /* === SEMANTIC COLORS === */
  
  /* Success - Ocean Green */
  --success-dark: #0D4F3C;
  --success: #10B981;
  --success-light: #6EE7B7;
  --success-bg: #ECFDF5;
  
  /* Warning - Sunset Orange */
  --warning-dark: #92400E;
  --warning: #F59E0B;
  --warning-light: #FCD34D;
  --warning-bg: #FFFBEB;
  
  /* Error - Coral Red */
  --error-dark: #991B1B;
  --error: #EF4444;
  --error-light: #FCA5A5;
  --error-bg: #FEF2F2;
  
  /* Info - Sky Blue */
  --info-dark: #1E40AF;
  --info: #3B82F6;
  --info-light: #93C5FD;
  --info-bg: #EFF6FF;
  
  /* === NEUTRAL PALETTE === */
  --neutral-900: #111827;          /* Darkest text */
  --neutral-800: #1F2937;          /* Dark text */
  --neutral-700: #374151;          /* Medium dark text */
  --neutral-600: #4B5563;          /* Medium text */
  --neutral-500: #6B7280;          /* Light text */
  --neutral-400: #9CA3AF;          /* Lighter text */
  --neutral-300: #D1D5DB;          /* Border color */
  --neutral-200: #E5E7EB;          /* Light border */
  --neutral-100: #F3F4F6;          /* Background */
  --neutral-50: #F9FAFB;           /* Lightest background */
  --white: #FFFFFF;                /* Pure white */
  
  /* === TYPOGRAPHY === */
  
  /* Font Families */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-secondary: 'Playfair Display', Georgia, serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */
  
  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
  
  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  
  /* === SPACING SCALE === */
  --space-0: 0;
  --space-1: 0.25rem;      /* 4px */
  --space-2: 0.5rem;       /* 8px */
  --space-3: 0.75rem;      /* 12px */
  --space-4: 1rem;         /* 16px */
  --space-5: 1.25rem;      /* 20px */
  --space-6: 1.5rem;       /* 24px */
  --space-8: 2rem;         /* 32px */
  --space-10: 2.5rem;      /* 40px */
  --space-12: 3rem;        /* 48px */
  --space-16: 4rem;        /* 64px */
  --space-20: 5rem;        /* 80px */
  --space-24: 6rem;        /* 96px */
  
  /* === BORDER RADIUS === */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;
  
  /* === SHADOWS === */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.05);
  --shadow-ocean: 0 8px 32px rgba(11, 36, 71, 0.15);
  
  /* === TRANSITIONS === */
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* === BREAKPOINTS === */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* === Z-INDEX SCALE === */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ===== GLOBAL STYLES ===== */

* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-primary);
  line-height: var(--leading-normal);
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  background: var(--gradient-background);
  color: var(--neutral-800);
  font-size: var(--text-base);
}

/* ===== TYPOGRAPHY UTILITIES ===== */

.heading-1 {
  font-family: var(--font-secondary);
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  color: var(--ocean-deep);
  margin: 0 0 var(--space-6) 0;
}

.heading-2 {
  font-family: var(--font-primary);
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--ocean-medium);
  margin: 0 0 var(--space-4) 0;
}

.heading-3 {
  font-family: var(--font-primary);
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--neutral-800);
  margin: 0 0 var(--space-3) 0;
}

.body-large {
  font-size: var(--text-lg);
  font-weight: var(--font-normal);
  line-height: var(--leading-relaxed);
  color: var(--neutral-700);
}

.body-base {
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--neutral-800);
}

.body-small {
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--neutral-600);
}

.text-muted {
  color: var(--neutral-500);
}

.text-emphasis {
  color: var(--ocean-deep);
  font-weight: var(--font-semibold);
}

/* ===== LAYOUT UTILITIES ===== */

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }

/* ===== SPACING UTILITIES ===== */

.p-0 { padding: var(--space-0); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.m-0 { margin: var(--space-0); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

/* ===== COMPONENT BASE STYLES ===== */

.card {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--space-6);
  transition: var(--transition-base);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  line-height: 1;
  border: none;
  cursor: pointer;
  transition: var(--transition-base);
  min-height: 44px;
}

.input-base {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: var(--transition-base);
  min-height: 44px;
}

.input-base:focus {
  outline: none;
  border-color: var(--ocean-medium);
  box-shadow: 0 0 0 3px rgba(87, 108, 188, 0.1);
}

/* ===== ACCESSIBILITY UTILITIES ===== */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible:focus-visible {
  outline: 2px solid var(--ocean-light);
  outline-offset: 2px;
}

/* ===== RESPONSIVE UTILITIES ===== */

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-3);
  }
  
  .heading-1 {
    font-size: var(--text-3xl);
  }
  
  .heading-2 {
    font-size: var(--text-xl);
  }
}

/* ===== ANIMATION UTILITIES ===== */

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease forwards;
}

.animate-slide-up {
  animation: slideUp 0.3s ease forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

/* ===== ACCESSIBILITY & PREFERENCES ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --neutral-300: #000000;
    --neutral-600: #000000;
    --ocean-medium: #000080;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode variables would go here */
}
