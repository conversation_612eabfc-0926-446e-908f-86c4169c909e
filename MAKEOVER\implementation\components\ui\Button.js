import { forwardRef } from 'react'
import { motion } from 'framer-motion'

/**
 * Modern Button Component for Ocean Soul Sparkles Admin Panel
 * 
 * Features:
 * - Multiple variants (primary, secondary, ghost, danger)
 * - Size options (sm, md, lg)
 * - Loading states with spinner
 * - Icon support (left and right)
 * - Accessibility compliant (WCAG 2.1 AA)
 * - Smooth animations and micro-interactions
 * - Touch-friendly (44px minimum height)
 */

const Button = forwardRef(({ 
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  leftIcon = null,
  rightIcon = null,
  children,
  className = '',
  fullWidth = false,
  ...props 
}, ref) => {
  const isDisabled = disabled || loading

  // Base classes for all buttons
  const baseClasses = `
    inline-flex items-center justify-center font-medium rounded-lg 
    transition-all duration-200 focus:outline-none focus:ring-2 
    focus:ring-offset-2 relative overflow-hidden cursor-pointer
    ${fullWidth ? 'w-full' : ''}
    ${isDisabled ? 'cursor-not-allowed' : ''}
  `

  // Variant styles
  const variants = {
    primary: `
      bg-gradient-to-r from-ocean-deep to-ocean-medium text-white 
      hover:shadow-lg hover:shadow-ocean-deep/25 hover:-translate-y-0.5 
      focus:ring-ocean-light active:translate-y-0
      ${isDisabled ? 'opacity-50 hover:shadow-none hover:translate-y-0' : ''}
    `,
    secondary: `
      bg-white text-ocean-medium border-2 border-ocean-light 
      hover:bg-ocean-surface hover:border-ocean-medium hover:shadow-md
      focus:ring-ocean-light active:bg-ocean-light/20
      ${isDisabled ? 'opacity-50 hover:bg-white hover:border-ocean-light hover:shadow-none' : ''}
    `,
    ghost: `
      text-ocean-medium hover:bg-ocean-surface hover:text-ocean-deep
      focus:ring-ocean-light active:bg-ocean-light/30
      ${isDisabled ? 'opacity-50 hover:bg-transparent hover:text-ocean-medium' : ''}
    `,
    danger: `
      bg-gradient-to-r from-error to-red-600 text-white 
      hover:shadow-lg hover:shadow-error/25 hover:-translate-y-0.5
      focus:ring-error/50 active:translate-y-0
      ${isDisabled ? 'opacity-50 hover:shadow-none hover:translate-y-0' : ''}
    `,
    success: `
      bg-gradient-to-r from-success to-green-600 text-white 
      hover:shadow-lg hover:shadow-success/25 hover:-translate-y-0.5
      focus:ring-success/50 active:translate-y-0
      ${isDisabled ? 'opacity-50 hover:shadow-none hover:translate-y-0' : ''}
    `,
    warning: `
      bg-gradient-to-r from-warning to-orange-600 text-white 
      hover:shadow-lg hover:shadow-warning/25 hover:-translate-y-0.5
      focus:ring-warning/50 active:translate-y-0
      ${isDisabled ? 'opacity-50 hover:shadow-none hover:translate-y-0' : ''}
    `
  }

  // Size styles
  const sizes = {
    sm: 'px-3 py-2 text-sm min-h-[36px] gap-1.5',
    md: 'px-4 py-3 text-sm min-h-[44px] gap-2',
    lg: 'px-6 py-4 text-base min-h-[48px] gap-2.5'
  }

  // Loading spinner component
  const LoadingSpinner = () => (
    <motion.svg
      className="animate-spin h-4 w-4"
      fill="none"
      viewBox="0 0 24 24"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <circle 
        className="opacity-25" 
        cx="12" 
        cy="12" 
        r="10" 
        stroke="currentColor" 
        strokeWidth="4" 
      />
      <path 
        className="opacity-75" 
        fill="currentColor" 
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" 
      />
    </motion.svg>
  )

  // Ripple effect for enhanced interaction feedback
  const handleClick = (e) => {
    if (isDisabled) return

    // Create ripple effect
    const button = e.currentTarget
    const rect = button.getBoundingClientRect()
    const size = Math.max(rect.width, rect.height)
    const x = e.clientX - rect.left - size / 2
    const y = e.clientY - rect.top - size / 2
    
    const ripple = document.createElement('span')
    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple 0.6s linear;
      pointer-events: none;
    `
    
    button.appendChild(ripple)
    
    setTimeout(() => {
      ripple.remove()
    }, 600)

    // Call original onClick if provided
    if (props.onClick) {
      props.onClick(e)
    }
  }

  return (
    <>
      <motion.button
        ref={ref}
        className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
        disabled={isDisabled}
        whileHover={!isDisabled ? { scale: 1.02 } : {}}
        whileTap={!isDisabled ? { scale: 0.98 } : {}}
        onClick={handleClick}
        {...props}
      >
        {/* Left Icon */}
        {leftIcon && !loading && (
          <span className="flex-shrink-0">
            {leftIcon}
          </span>
        )}

        {/* Loading Spinner */}
        {loading && <LoadingSpinner />}

        {/* Button Text */}
        <span className={loading ? 'opacity-70' : ''}>
          {children}
        </span>

        {/* Right Icon */}
        {rightIcon && !loading && (
          <span className="flex-shrink-0">
            {rightIcon}
          </span>
        )}

        {/* Shimmer effect for primary buttons */}
        {variant === 'primary' && !isDisabled && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
            initial={{ x: '-100%' }}
            whileHover={{ x: '100%' }}
            transition={{ duration: 0.6 }}
          />
        )}
      </motion.button>

      <style jsx>{`
        @keyframes ripple {
          to {
            transform: scale(4);
            opacity: 0;
          }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
          button {
            border: 2px solid currentColor !important;
          }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          button {
            transition: none !important;
          }
          
          button * {
            animation: none !important;
          }
        }

        /* Focus styles for keyboard navigation */
        button:focus-visible {
          outline: 2px solid var(--ocean-light);
          outline-offset: 2px;
        }

        /* Touch device optimizations */
        @media (hover: none) and (pointer: coarse) {
          button:hover {
            transform: none !important;
            box-shadow: none !important;
          }
        }
      `}</style>
    </>
  )
})

Button.displayName = 'Button'

// Export button variants as constants for consistency
export const BUTTON_VARIANTS = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
  GHOST: 'ghost',
  DANGER: 'danger',
  SUCCESS: 'success',
  WARNING: 'warning'
}

export const BUTTON_SIZES = {
  SMALL: 'sm',
  MEDIUM: 'md',
  LARGE: 'lg'
}

export default Button
